"""
شاشة الدخول المتقدمة والكبيرة للبرنامج مع تأثيرات بصرية متطورة
"""

import sys
import math
import random
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QFrame, QApplication,
                             QGraphicsDropShadowEffect, QCheckBox, QSpacerItem,
                             QSizePolicy, QGridLayout, QMessageBox, QProgressBar,
                             QGraphicsOpacityEffect, QDesktopWidget)
from PyQt5.QtCore import (Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal,
                          QRect, QParallelAnimationGroup, QSequentialAnimationGroup,
                          QPoint, QSize, QThread, pyqtProperty)
from PyQt5.QtGui import (QFont, QPixmap, QPainter, QBrush, QColor, QPen,
                         QLinearGradient, QRadialGradient, QPainterPath, QPolygon,
                         QFontMetrics, QMovie, QIcon)
from database import get_session, User
import hashlib


class ParticleSystem(QWidget):
    """نظام الجسيمات المتحركة للخلفية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.particles = []
        self.init_particles()

        # مؤقت لتحديث الجسيمات
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_particles)
        self.timer.start(50)  # تحديث كل 50 مللي ثانية

    def init_particles(self):
        """إنشاء الجسيمات الأولية"""
        for _ in range(50):  # 50 جسيم
            particle = {
                'x': random.randint(0, 1400),
                'y': random.randint(0, 900),
                'vx': random.uniform(-2, 2),
                'vy': random.uniform(-2, 2),
                'size': random.randint(2, 8),
                'color': random.choice([
                    QColor(59, 130, 246, 100),   # أزرق
                    QColor(139, 92, 246, 100),   # بنفسجي
                    QColor(16, 185, 129, 100),   # أخضر
                    QColor(245, 158, 11, 100),   # ذهبي
                    QColor(239, 68, 68, 100),    # أحمر
                    QColor(255, 255, 255, 80),   # أبيض شفاف
                ]),
                'pulse': random.uniform(0, 2 * math.pi)
            }
            self.particles.append(particle)

    def update_particles(self):
        """تحديث مواضع الجسيمات"""
        for particle in self.particles:
            # تحديث الموضع
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['pulse'] += 0.1

            # إعادة تدوير الجسيمات عند الخروج من الحدود
            if particle['x'] < -10:
                particle['x'] = 1410
            elif particle['x'] > 1410:
                particle['x'] = -10

            if particle['y'] < -10:
                particle['y'] = 910
            elif particle['y'] > 910:
                particle['y'] = -10

        self.update()

    def paintEvent(self, event):
        """رسم الجسيمات"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        for particle in self.particles:
            # تأثير النبض
            pulse_factor = (math.sin(particle['pulse']) + 1) / 2
            size = particle['size'] * (0.5 + pulse_factor * 0.5)

            # رسم الجسيم
            painter.setBrush(QBrush(particle['color']))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(
                int(particle['x'] - size/2),
                int(particle['y'] - size/2),
                int(size),
                int(size)
            )


class AnimatedBackground(QWidget):
    """خلفية متحركة مع تدرجات ديناميكية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.gradient_offset = 0

        # مؤقت لتحريك التدرج
        self.timer = QTimer()
        self.timer.timeout.connect(self.animate_gradient)
        self.timer.start(100)

    def animate_gradient(self):
        """تحريك التدرج"""
        self.gradient_offset += 0.01
        if self.gradient_offset > 1:
            self.gradient_offset = 0
        self.update()

    def paintEvent(self, event):
        """رسم الخلفية المتحركة"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # إنشاء تدرج متحرك
        gradient = QLinearGradient(0, 0, self.width(), self.height())

        # ألوان متغيرة بناءً على الوقت
        colors = [
            (0.0 + self.gradient_offset) % 1.0, QColor(15, 23, 42),
            (0.2 + self.gradient_offset) % 1.0, QColor(30, 41, 59),
            (0.4 + self.gradient_offset) % 1.0, QColor(37, 99, 235),
            (0.6 + self.gradient_offset) % 1.0, QColor(139, 92, 246),
            (0.8 + self.gradient_offset) % 1.0, QColor(124, 58, 237),
            (1.0 + self.gradient_offset) % 1.0, QColor(91, 33, 182),
        ]

        for pos, color in colors:
            if pos <= 1.0:
                gradient.setColorAt(pos, color)

        painter.fillRect(self.rect(), QBrush(gradient))


class LoginScreen(QWidget):
    """شاشة الدخول المتقدمة والكبيرة مع تأثيرات بصرية متطورة"""

    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user

    def __init__(self):
        super().__init__()
        self.session = None
        self.login_attempts = 0
        self.max_attempts = 3
        self.is_loading = False

        # إعداد الواجهة والرسوم المتحركة
        self.setup_ui()
        self.setup_animations()
        self.setup_advanced_effects()

    def setup_advanced_effects(self):
        """إعداد التأثيرات المتقدمة"""
        # إضافة نظام الجسيمات
        self.particle_system = ParticleSystem(self)
        self.particle_system.setGeometry(0, 0, 1400, 900)
        self.particle_system.lower()  # وضعها في الخلف

        # إضافة الخلفية المتحركة
        self.animated_bg = AnimatedBackground(self)
        self.animated_bg.setGeometry(0, 0, 1400, 900)
        self.animated_bg.lower()  # وضعها في الخلف

        # تأثيرات الإضاءة المتقدمة
        self.setup_lighting_effects()

    def setup_lighting_effects(self):
        """إعداد تأثيرات الإضاءة المتقدمة"""
        # تأثير الوهج للإطار الرئيسي
        self.glow_effect = QGraphicsDropShadowEffect()
        self.glow_effect.setBlurRadius(50)
        self.glow_effect.setColor(QColor(59, 130, 246, 150))
        self.glow_effect.setOffset(0, 0)

        # رسوم متحركة للوهج
        self.glow_animation = QPropertyAnimation(self.glow_effect, b"color")
        self.glow_animation.setDuration(3000)
        self.glow_animation.setStartValue(QColor(59, 130, 246, 150))
        self.glow_animation.setEndValue(QColor(139, 92, 246, 150))
        self.glow_animation.setLoopCount(-1)  # تكرار لا نهائي
        self.glow_animation.start()
        
    def setup_ui(self):
        """إعداد واجهة شاشة الدخول المتقدمة"""
        # إعداد النافذة الأساسية
        self.setWindowTitle("🔐 Smart Finish - نظام تسجيل الدخول المتطور")
        self.setFixedSize(1400, 900)  # حجم كبير ومتقدم
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # توسيط النافذة في الشاشة
        self.center_window()

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء الإطار الرئيسي مع التدرج المتقدم والشفافية
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.main_frame.setStyleSheet("""
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.95), stop:0.1 rgba(30, 41, 59, 0.95),
                    stop:0.2 rgba(51, 65, 85, 0.95), stop:0.3 rgba(71, 85, 105, 0.95),
                    stop:0.4 rgba(37, 99, 235, 0.95), stop:0.5 rgba(59, 130, 246, 0.95),
                    stop:0.6 rgba(96, 165, 250, 0.95), stop:0.7 rgba(139, 92, 246, 0.95),
                    stop:0.8 rgba(124, 58, 237, 0.95), stop:0.9 rgba(109, 40, 217, 0.95),
                    stop:1 rgba(91, 33, 182, 0.95));
                border-radius: 30px;
                border: 4px solid rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(20px);
            }
        """)

        # إضافة تأثير الظل المتقدم للإطار الرئيسي
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(50)
        shadow_effect.setColor(QColor(0, 0, 0, 150))
        shadow_effect.setOffset(0, 15)
        self.main_frame.setGraphicsEffect(shadow_effect)

        main_layout.addWidget(self.main_frame)

        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(60, 50, 60, 50)
        frame_layout.setSpacing(35)

        # إنشاء شريط التحكم العلوي
        self.create_control_bar(frame_layout)

        # إنشاء منطقة العنوان المحسنة
        self.create_enhanced_header_section(frame_layout)

        # إنشاء منطقة تسجيل الدخول المحسنة
        self.create_enhanced_login_section(frame_layout)

        # إنشاء شريط التقدم
        self.create_progress_section(frame_layout)

        # إنشاء منطقة الأزرار المحسنة
        self.create_enhanced_buttons_section(frame_layout)

        # إنشاء منطقة التذييل المحسنة
        self.create_enhanced_footer_section(frame_layout)

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def create_control_bar(self, parent_layout):
        """إنشاء شريط التحكم العلوي"""
        control_frame = QFrame()
        control_frame.setFixedHeight(50)
        control_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)

        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(20, 10, 20, 10)

        # عنوان النافذة
        title_label = QLabel("🔐 Smart Finish - تسجيل الدخول")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
        """)

        # أزرار التحكم
        minimize_btn = QPushButton("🗕")
        close_btn = QPushButton("✕")

        for btn in [minimize_btn, close_btn]:
            btn.setFixedSize(30, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 15px;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            """)

        minimize_btn.clicked.connect(self.showMinimized)
        close_btn.clicked.connect(self.close)

        control_layout.addWidget(title_label)
        control_layout.addStretch()
        control_layout.addWidget(minimize_btn)
        control_layout.addWidget(close_btn)

        parent_layout.addWidget(control_frame)

    def create_enhanced_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المحسنة مع تأثيرات متقدمة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(250)
        header_frame.setStyleSheet("""
            QFrame {
                background: qradial-gradient(circle,
                    rgba(255, 255, 255, 0.15) 0%,
                    rgba(59, 130, 246, 0.2) 30%,
                    rgba(139, 92, 246, 0.15) 70%,
                    rgba(255, 255, 255, 0.1) 100%);
                border-radius: 25px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        # تأثير الظل للعنوان
        header_shadow = QGraphicsDropShadowEffect()
        header_shadow.setBlurRadius(25)
        header_shadow.setColor(QColor(59, 130, 246, 100))
        header_shadow.setOffset(0, 8)
        header_frame.setGraphicsEffect(header_shadow)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(20)

        # شعار متحرك مع تأثيرات
        logo_frame = QFrame()
        logo_frame.setFixedSize(120, 120)
        logo_frame.setStyleSheet("""
            QFrame {
                background: qradial-gradient(circle,
                    rgba(255, 215, 0, 0.8) 0%,
                    rgba(59, 130, 246, 0.6) 50%,
                    rgba(139, 92, 246, 0.4) 100%);
                border-radius: 60px;
                border: 3px solid rgba(255, 255, 255, 0.5);
            }
        """)

        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setAlignment(Qt.AlignCenter)

        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 72px;
                background: transparent;
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            }
        """)
        logo_layout.addWidget(logo_label)

        # العنوان الرئيسي مع تأثير متدرج
        title_label = QLabel("🏢 Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 52px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 6px 12px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(59, 130, 246, 0.4);
                background: transparent;
                padding: 15px;
            }
        """)

        # العنوان الفرعي مع تأثير لامع
        subtitle_label = QLabel("نظام المحاسبة الإداري المتطور والشامل")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.95);
                font-size: 26px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4),
                           0 1px 2px rgba(139, 92, 246, 0.3);
                background: transparent;
                padding: 12px;
            }
        """)

        # شعار إضافي للتميز
        badge_label = QLabel("⭐ الإصدار المتطور ⭐")
        badge_label.setAlignment(Qt.AlignCenter)
        badge_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 215, 0, 1.0);
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 215, 0, 0.2),
                    stop:0.5 rgba(255, 255, 255, 0.3),
                    stop:1 rgba(255, 215, 0, 0.2));
                border-radius: 15px;
                padding: 8px 20px;
                border: 1px solid rgba(255, 215, 0, 0.5);
            }
        """)

        header_layout.addWidget(logo_frame, 0, Qt.AlignCenter)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addWidget(badge_label)

        parent_layout.addWidget(header_frame)

        # إضافة رسوم متحركة للشعار
        self.setup_logo_animation(logo_frame)

    def setup_logo_animation(self, logo_frame):
        """إعداد رسوم متحركة للشعار"""
        # رسوم متحركة للدوران
        self.logo_rotation = QPropertyAnimation(logo_frame, b"geometry")
        self.logo_rotation.setDuration(4000)
        self.logo_rotation.setLoopCount(-1)

        # تأثير النبض
        self.logo_pulse = QPropertyAnimation(logo_frame, b"minimumSize")
        self.logo_pulse.setDuration(2000)
        self.logo_pulse.setStartValue(QSize(120, 120))
        self.logo_pulse.setEndValue(QSize(130, 130))
        self.logo_pulse.setLoopCount(-1)
        self.logo_pulse.start()

    def create_enhanced_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المحسنة"""
        login_frame = QFrame()
        login_frame.setFixedHeight(350)
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(59, 130, 246, 0.1),
                    stop:0.7 rgba(139, 92, 246, 0.1),
                    stop:1 rgba(255, 255, 255, 0.15));
                border-radius: 25px;
                border: 3px solid rgba(255, 255, 255, 0.25);
                backdrop-filter: blur(15px);
            }
        """)

        # تأثير الظل المتقدم
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(30)
        login_shadow.setColor(QColor(0, 0, 0, 80))
        login_shadow.setOffset(0, 8)
        login_frame.setGraphicsEffect(login_shadow)

        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(50, 40, 50, 40)
        login_layout.setSpacing(25)

        # عنوان قسم تسجيل الدخول
        section_title = QLabel("🔑 تسجيل الدخول إلى النظام")
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.3),
                    stop:1 rgba(59, 130, 246, 0.3));
                border-radius: 15px;
                padding: 15px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        # حاوية حقول الإدخال
        inputs_frame = QFrame()
        inputs_layout = QGridLayout(inputs_frame)
        inputs_layout.setSpacing(20)

        # حقل اسم المستخدم المحسن
        username_container = self.create_input_container("👤", "اسم المستخدم", "admin")
        self.username_input = username_container['input']

        # حقل كلمة المرور المحسن
        password_container = self.create_input_container("🔒", "كلمة المرور", "admin", is_password=True)
        self.password_input = password_container['input']

        inputs_layout.addWidget(username_container['frame'], 0, 0)
        inputs_layout.addWidget(password_container['frame'], 1, 0)

        # خيارات إضافية
        options_frame = QFrame()
        options_layout = QHBoxLayout(options_frame)
        options_layout.setSpacing(30)

        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(True)
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 18px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                spacing: 12px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QCheckBox::indicator {
                width: 24px;
                height: 24px;
                border-radius: 12px;
                border: 3px solid rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.15);
            }
            QCheckBox::indicator:checked {
                background: qradial-gradient(circle,
                    rgba(16, 185, 129, 1.0) 0%,
                    rgba(5, 150, 105, 0.8) 100%);
                border: 3px solid #10B981;
            }
            QCheckBox::indicator:hover {
                border: 3px solid rgba(255, 255, 255, 0.8);
                background: rgba(255, 255, 255, 0.25);
            }
        """)

        # رابط نسيان كلمة المرور
        forgot_label = QLabel("🔗 نسيت كلمة المرور؟")
        forgot_label.setStyleSheet("""
            QLabel {
                color: rgba(96, 165, 250, 1.0);
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-decoration: underline;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                color: rgba(147, 197, 253, 1.0);
            }
        """)
        forgot_label.setCursor(Qt.PointingHandCursor)

        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(forgot_label)

        login_layout.addWidget(section_title)
        login_layout.addWidget(inputs_frame)
        login_layout.addWidget(options_frame)

        parent_layout.addWidget(login_frame)

    def create_input_container(self, icon, placeholder, default_value="", is_password=False):
        """إنشاء حاوية حقل إدخال محسنة"""
        container_frame = QFrame()
        container_frame.setFixedHeight(80)
        container_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(59, 130, 246, 0.6);
            }
        """)

        container_layout = QHBoxLayout(container_frame)
        container_layout.setContentsMargins(20, 15, 20, 15)
        container_layout.setSpacing(15)

        # أيقونة الحقل
        icon_label = QLabel(icon)
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                background: qradial-gradient(circle,
                    rgba(59, 130, 246, 0.3) 0%,
                    rgba(139, 92, 246, 0.2) 100%);
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.4);
            }
        """)

        # حقل الإدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText(f"أدخل {placeholder}...")
        input_field.setText(default_value)
        if is_password:
            input_field.setEchoMode(QLineEdit.Password)

        input_field.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(59, 130, 246, 0.4);
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 3px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
            }
        """)

        container_layout.addWidget(icon_label)
        container_layout.addWidget(input_field)

        return {'frame': container_frame, 'input': input_field}

    def create_progress_section(self, parent_layout):
        """إنشاء شريط التقدم"""
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                border: none;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 4px;
            }
        """)

        parent_layout.addWidget(self.progress_bar)

    def create_enhanced_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المحسنة"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(140)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(30, 25, 30, 25)
        buttons_layout.setSpacing(40)

        # زر تسجيل الدخول المحسن
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setFixedSize(250, 70)
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.3 #059669, stop:0.7 #047857, stop:1 #065F46);
                border: 3px solid #10B981;
                border-radius: 20px;
                color: white;
                font-size: 20px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34D399, stop:0.3 #10B981, stop:0.7 #059669, stop:1 #047857);
                border: 3px solid #34D399;
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(52, 211, 153, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:0.3 #065F46, stop:0.7 #064E3B, stop:1 #022C22);
                border: 3px solid #047857;
                transform: translateY(2px);
                box-shadow: 0 3px 8px rgba(4, 120, 87, 0.5);
            }
            QPushButton:disabled {
                background: rgba(107, 114, 128, 0.5);
                border: 3px solid rgba(107, 114, 128, 0.3);
                color: rgba(255, 255, 255, 0.5);
            }
        """)
        self.login_button.clicked.connect(self.handle_enhanced_login)

        # زر الإلغاء المحسن
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFixedSize(200, 70)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.3 #B91C1C, stop:0.7 #991B1B, stop:1 #7F1D1D);
                border: 3px solid #DC2626;
                border-radius: 20px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                box-shadow: 0 6px 15px rgba(220, 38, 38, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F87171, stop:0.3 #EF4444, stop:0.7 #DC2626, stop:1 #B91C1C);
                border: 3px solid #F87171;
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(248, 113, 113, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991B1B, stop:0.3 #7F1D1D, stop:0.7 #450A0A, stop:1 #1C0A0A);
                border: 3px solid #991B1B;
                transform: translateY(2px);
                box-shadow: 0 3px 8px rgba(153, 27, 27, 0.5);
            }
        """)
        self.cancel_button.clicked.connect(self.close)

        # زر إضافي للمساعدة
        help_button = QPushButton("❓ مساعدة")
        help_button.setFixedSize(150, 70)
        help_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #8B5CF6, stop:0.5 #7C3AED, stop:1 #6D28D9);
                border: 3px solid #8B5CF6;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #A78BFA, stop:0.5 #8B5CF6, stop:1 #7C3AED);
                border: 3px solid #A78BFA;
                transform: translateY(-2px);
            }
        """)
        help_button.clicked.connect(self.show_help_dialog)

        # ترتيب الأزرار
        buttons_layout.addStretch()
        buttons_layout.addWidget(help_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()

        parent_layout.addWidget(buttons_frame)

    def create_enhanced_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المحسنة"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(120)
        footer_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.05),
                    stop:0.5 rgba(59, 130, 246, 0.1),
                    stop:1 rgba(255, 255, 255, 0.05));
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)

        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(12)

        # معلومات المطور المحسنة
        developer_label = QLabel("👨‍💻 تطوير: المهندس خالد محسن")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 20px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 215, 0, 0.2),
                    stop:0.5 rgba(59, 130, 246, 0.2),
                    stop:1 rgba(255, 215, 0, 0.2));
                border-radius: 12px;
                padding: 10px 25px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
        """)

        # معلومات حقوق الطبع المحسنة
        copyright_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 2.0 - المتطور والشامل")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(96, 165, 250, 0.9);
                font-size: 14px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)

        footer_layout.addWidget(developer_label)
        footer_layout.addWidget(copyright_label)
        footer_layout.addWidget(version_label)

        parent_layout.addWidget(footer_frame)

    def show_help_dialog(self):
        """عرض نافذة المساعدة"""
        help_msg = QMessageBox(self)
        help_msg.setWindowTitle("💡 مساعدة تسجيل الدخول")
        help_msg.setText("""
        🔐 معلومات تسجيل الدخول:

        👤 اسم المستخدم الافتراضي: admin
        🔒 كلمة المرور الافتراضية: admin

        📝 ملاحظات:
        • تأكد من صحة البيانات المدخلة
        • يمكنك تذكر بيانات الدخول للمرات القادمة
        • في حالة نسيان كلمة المرور، اتصل بالمطور

        🛠️ للدعم التقني:
        المهندس خالد محسن
        """)
        help_msg.setIcon(QMessageBox.Information)
        help_msg.setStyleSheet(self.get_help_dialog_style())
        help_msg.exec_()

    def get_help_dialog_style(self):
        """الحصول على نمط نافذة المساعدة"""
        return """
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #8B5CF6, stop:0.5 #7C3AED, stop:1 #6D28D9);
                border-radius: 15px;
                border: 3px solid #A78BFA;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                text-align: right;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #8B5CF6;
                border-radius: 10px;
                color: #6D28D9;
                font-weight: bold;
                padding: 10px 20px;
                min-width: 100px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #A78BFA;
            }
        """

    def create_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المتقدمة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(200)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel("🏢 Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 48px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.5 rgba(255, 215, 0, 0.8),
                    stop:1 rgba(255, 255, 255, 0.9));
                -webkit-background-clip: text;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        # العنوان الفرعي
        subtitle_label = QLabel("نظام المحاسبة الإداري المتطور والشامل")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 24px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 10px;
            }
        """)
        
        # شعار أو أيقونة متقدمة
        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 64px;
                padding: 15px;
                background: qradial-gradient(circle,
                    rgba(255, 255, 255, 0.2) 0%,
                    rgba(59, 130, 246, 0.3) 50%,
                    rgba(139, 92, 246, 0.2) 100%);
                border-radius: 50px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المتقدمة"""
        login_frame = QFrame()
        login_frame.setFixedHeight(300)
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.1));
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        # إضافة تأثير الظل لإطار تسجيل الدخول
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(20)
        login_shadow.setColor(QColor(0, 0, 0, 50))
        login_shadow.setOffset(0, 5)
        login_frame.setGraphicsEffect(login_shadow)
        
        login_layout = QGridLayout(login_frame)
        login_layout.setContentsMargins(40, 30, 40, 30)
        login_layout.setSpacing(20)
        
        # تسمية اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet(self.get_label_style())
        
        # حقل اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_input.setText("admin")  # قيمة افتراضية
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setFixedHeight(50)
        
        # تسمية كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setStyleSheet(self.get_label_style())
        
        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        self.password_input.setText("admin")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setFixedHeight(50)
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(True)
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                border: 2px solid #10B981;
            }
        """)
        
        # ترتيب العناصر في الشبكة
        login_layout.addWidget(username_label, 0, 0)
        login_layout.addWidget(self.username_input, 0, 1)
        login_layout.addWidget(password_label, 1, 0)
        login_layout.addWidget(self.password_input, 1, 1)
        login_layout.addWidget(self.remember_checkbox, 2, 0, 1, 2, Qt.AlignCenter)
        
        parent_layout.addWidget(login_frame)
        
    def create_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المتقدمة"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(120)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(30)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setFixedSize(200, 60)
        self.login_button.setStyleSheet(self.get_primary_button_style())
        self.login_button.clicked.connect(self.handle_login)
        
        # زر الإلغاء/الخروج
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFixedSize(200, 60)
        self.cancel_button.setStyleSheet(self.get_secondary_button_style())
        self.cancel_button.clicked.connect(self.close)
        
        # إضافة مساحة مرنة
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()
        
        parent_layout.addWidget(buttons_frame)

    def create_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المتقدمة"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(100)
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(10)

        # معلومات حقوق الطبع
        copyright_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
            }
        """)

        # معلومات المطور
        developer_label = QLabel("👨‍💻 تطوير: المهندس خالد محسن")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)

        footer_layout.addWidget(developer_label)
        footer_layout.addWidget(copyright_label)

        parent_layout.addWidget(footer_frame)

    def get_label_style(self):
        """الحصول على نمط التسميات"""
        return """
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 5px;
            }
        """

    def get_input_style(self):
        """الحصول على نمط حقول الإدخال"""
        return """
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 12px;
                padding: 12px 15px;
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 2px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
            }
        """

    def get_primary_button_style(self):
        """الحصول على نمط الزر الأساسي"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border: 2px solid #10B981;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34D399, stop:0.5 #10B981, stop:1 #059669);
                border: 2px solid #34D399;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:0.5 #065F46, stop:1 #064E3B);
                border: 2px solid #047857;
                transform: translateY(1px);
            }
        """

    def get_secondary_button_style(self):
        """الحصول على نمط الزر الثانوي"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border: 2px solid #DC2626;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F87171, stop:0.5 #EF4444, stop:1 #DC2626);
                border: 2px solid #F87171;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991B1B, stop:0.5 #7F1D1D, stop:1 #450A0A);
                border: 2px solid #991B1B;
                transform: translateY(1px);
            }
        """

    def setup_animations(self):
        """إعداد الرسوم المتحركة المتقدمة"""
        # رسوم متحركة لظهور النافذة مع تأثير التلاشي
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # رسوم متحركة لحركة النافذة مع تأثير الارتداد
        self.move_animation = QPropertyAnimation(self, b"geometry")
        self.move_animation.setDuration(1200)
        self.move_animation.setEasingCurve(QEasingCurve.OutElastic)

        # رسوم متحركة لتكبير النافذة
        self.scale_animation = QPropertyAnimation(self, b"size")
        self.scale_animation.setDuration(1000)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)

        # مجموعة رسوم متحركة متوازية
        self.entrance_group = QParallelAnimationGroup()
        self.entrance_group.addAnimation(self.fade_animation)
        self.entrance_group.addAnimation(self.move_animation)
        self.entrance_group.addAnimation(self.scale_animation)

        # رسوم متحركة للخلفية
        self.setup_background_animations()

        # بدء الرسوم المتحركة عند الظهور
        QTimer.singleShot(100, self.start_enhanced_entrance_animation)

    def setup_background_animations(self):
        """إعداد رسوم متحركة للخلفية"""
        # تأثير التموج للخلفية
        self.background_wave_timer = QTimer()
        self.background_wave_timer.timeout.connect(self.animate_background_wave)
        self.background_wave_timer.start(200)

        # متغير لتتبع موجة الخلفية
        self.wave_offset = 0

    def animate_background_wave(self):
        """تحريك موجة الخلفية"""
        self.wave_offset += 0.1
        if self.wave_offset > 2 * math.pi:
            self.wave_offset = 0
        # يمكن إضافة تأثيرات إضافية هنا

    def start_enhanced_entrance_animation(self):
        """بدء رسوم متحركة محسنة للدخول"""
        # تحديد الموضع النهائي (وسط الشاشة)
        screen = QDesktopWidget().screenGeometry()
        final_x = (screen.width() - self.width()) // 2
        final_y = (screen.height() - self.height()) // 2

        # تحديد المواضع للرسوم المتحركة
        start_rect = QRect(final_x, -self.height() - 100, self.width(), self.height())
        end_rect = QRect(final_x, final_y, self.width(), self.height())

        # إعداد رسوم متحركة الحركة
        self.setGeometry(start_rect)
        self.move_animation.setStartValue(start_rect)
        self.move_animation.setEndValue(end_rect)

        # إعداد رسوم متحركة التكبير
        start_size = QSize(int(self.width() * 0.8), int(self.height() * 0.8))
        end_size = QSize(self.width(), self.height())
        self.scale_animation.setStartValue(start_size)
        self.scale_animation.setEndValue(end_size)

        # بدء مجموعة الرسوم المتحركة
        self.entrance_group.start()

        # إضافة تأثيرات صوتية (اختيارية)
        self.add_entrance_effects()

    def add_entrance_effects(self):
        """إضافة تأثيرات إضافية للدخول"""
        # تأثير الوميض للحدود
        self.border_flash_timer = QTimer()
        self.border_flash_timer.timeout.connect(self.flash_border)
        self.border_flash_timer.start(500)

        # إيقاف التأثير بعد 3 ثوانٍ
        QTimer.singleShot(3000, self.border_flash_timer.stop)

    def flash_border(self):
        """تأثير وميض الحدود"""
        # تغيير لون الحدود بشكل دوري
        colors = [
            "rgba(59, 130, 246, 0.8)",
            "rgba(139, 92, 246, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(245, 158, 11, 0.8)"
        ]

        import random
        color = random.choice(colors)

        # تطبيق اللون الجديد مؤقتاً
        current_style = self.main_frame.styleSheet()
        # يمكن إضافة تغيير اللون هنا إذا لزم الأمر

    def start_entrance_animation(self):
        """بدء رسوم متحركة للدخول (الإصدار القديم)"""
        # استدعاء الإصدار المحسن
        self.start_enhanced_entrance_animation()

    def handle_enhanced_login(self):
        """معالجة عملية تسجيل الدخول المحسنة مع التأثيرات"""
        if self.is_loading:
            return

        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_enhanced_error_message("⚠️ خطأ في البيانات",
                                           "يرجى إدخال اسم المستخدم وكلمة المرور")
            self.shake_login_frame()
            return

        # التحقق من عدد المحاولات
        if self.login_attempts >= self.max_attempts:
            self.show_enhanced_error_message("🚫 تم تجاوز الحد الأقصى",
                                           f"تم تجاوز الحد الأقصى للمحاولات ({self.max_attempts})")
            return

        # بدء عملية التحميل
        self.start_loading_animation()

        try:
            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                # نجح تسجيل الدخول
                self.login_attempts = 0  # إعادة تعيين المحاولات
                self.show_enhanced_success_message("✅ نجح تسجيل الدخول",
                                                 f"مرحباً بك {user.full_name}")

                # تأثيرات النجاح
                self.success_animation()

                # إرسال إشارة نجاح تسجيل الدخول
                QTimer.singleShot(2000, lambda: self.login_successful.emit(self.session, user))
                QTimer.singleShot(2000, self.close)

            else:
                # فشل تسجيل الدخول
                self.login_attempts += 1
                remaining = self.max_attempts - self.login_attempts

                if remaining > 0:
                    self.show_enhanced_error_message("❌ خطأ في تسجيل الدخول",
                                                   f"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {remaining}")
                else:
                    self.show_enhanced_error_message("🚫 تم حظر تسجيل الدخول",
                                                   "تم تجاوز الحد الأقصى للمحاولات الخاطئة")

                # تأثيرات الفشل
                self.failure_animation()

        except Exception as e:
            self.show_enhanced_error_message("❌ خطأ في النظام",
                                           f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")
            self.failure_animation()

        finally:
            # إيقاف عملية التحميل
            self.stop_loading_animation()

    def start_loading_animation(self):
        """بدء رسوم متحركة للتحميل"""
        self.is_loading = True
        self.login_button.setEnabled(False)
        self.login_button.setText("⏳ جاري التحقق...")

        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم لا نهائي

        # تأثير النبض للزر
        self.button_pulse_animation = QPropertyAnimation(self.login_button, b"geometry")
        self.button_pulse_animation.setDuration(1000)
        self.button_pulse_animation.setLoopCount(-1)

    def stop_loading_animation(self):
        """إيقاف رسوم متحركة للتحميل"""
        self.is_loading = False
        self.login_button.setEnabled(True)
        self.login_button.setText("🚀 تسجيل الدخول")

        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)

        # إيقاف تأثير النبض
        if hasattr(self, 'button_pulse_animation'):
            self.button_pulse_animation.stop()

    def success_animation(self):
        """رسوم متحركة للنجاح"""
        # تأثير الوهج الأخضر
        success_effect = QGraphicsDropShadowEffect()
        success_effect.setBlurRadius(40)
        success_effect.setColor(QColor(16, 185, 129, 200))
        success_effect.setOffset(0, 0)
        self.main_frame.setGraphicsEffect(success_effect)

        # رسوم متحركة للتكبير
        self.success_scale = QPropertyAnimation(self.main_frame, b"geometry")
        self.success_scale.setDuration(500)
        current_geo = self.main_frame.geometry()
        self.success_scale.setStartValue(current_geo)
        self.success_scale.setEndValue(QRect(
            current_geo.x() - 10, current_geo.y() - 10,
            current_geo.width() + 20, current_geo.height() + 20
        ))
        self.success_scale.start()

    def failure_animation(self):
        """رسوم متحركة للفشل"""
        # تأثير الوهج الأحمر
        failure_effect = QGraphicsDropShadowEffect()
        failure_effect.setBlurRadius(40)
        failure_effect.setColor(QColor(220, 38, 38, 200))
        failure_effect.setOffset(0, 0)
        self.main_frame.setGraphicsEffect(failure_effect)

        # إعادة التأثير الأصلي بعد ثانيتين
        QTimer.singleShot(2000, lambda: self.main_frame.setGraphicsEffect(self.glow_effect))

    def shake_login_frame(self):
        """تأثير الاهتزاز لإطار تسجيل الدخول"""
        # رسوم متحركة للاهتزاز
        self.shake_animation = QPropertyAnimation(self.main_frame, b"geometry")
        self.shake_animation.setDuration(500)

        original_geo = self.main_frame.geometry()
        positions = [
            QRect(original_geo.x() + 10, original_geo.y(), original_geo.width(), original_geo.height()),
            QRect(original_geo.x() - 10, original_geo.y(), original_geo.width(), original_geo.height()),
            QRect(original_geo.x() + 5, original_geo.y(), original_geo.width(), original_geo.height()),
            QRect(original_geo.x() - 5, original_geo.y(), original_geo.width(), original_geo.height()),
            original_geo
        ]

        # تطبيق الاهتزاز
        for i, pos in enumerate(positions):
            QTimer.singleShot(i * 100, lambda p=pos: self.main_frame.setGeometry(p))

    def handle_login(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_error_message("⚠️ خطأ في البيانات",
                                   "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                # نجح تسجيل الدخول
                self.show_success_message("✅ نجح تسجيل الدخول",
                                        f"مرحباً بك {user.full_name}")

                # إرسال إشارة نجاح تسجيل الدخول
                QTimer.singleShot(1500, lambda: self.login_successful.emit(self.session, user))
                QTimer.singleShot(1500, self.close)

            else:
                # فشل تسجيل الدخول
                self.show_error_message("❌ خطأ في تسجيل الدخول",
                                       "اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message("❌ خطأ في النظام",
                                   f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def show_enhanced_success_message(self, title, message):
        """عرض رسالة نجاح محسنة مع تأثيرات متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qradial-gradient(circle,
                    rgba(16, 185, 129, 0.95) 0%,
                    rgba(5, 150, 105, 0.9) 50%,
                    rgba(4, 120, 87, 0.85) 100%);
                border-radius: 20px;
                border: 4px solid #34D399;
                min-width: 400px;
                min-height: 200px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                text-align: center;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(243, 244, 246, 0.9));
                border: 3px solid #10B981;
                border-radius: 12px;
                color: #047857;
                font-weight: bold;
                font-size: 16px;
                padding: 12px 24px;
                min-width: 120px;
                text-shadow: none;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 3px solid #34D399;
                transform: translateY(-2px);
            }
        """)

        # إضافة تأثير الظل للرسالة
        msg_shadow = QGraphicsDropShadowEffect()
        msg_shadow.setBlurRadius(30)
        msg_shadow.setColor(QColor(16, 185, 129, 150))
        msg_shadow.setOffset(0, 10)
        msg_box.setGraphicsEffect(msg_shadow)

        msg_box.exec_()

    def show_enhanced_error_message(self, title, message):
        """عرض رسالة خطأ محسنة مع تأثيرات متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qradial-gradient(circle,
                    rgba(220, 38, 38, 0.95) 0%,
                    rgba(185, 28, 28, 0.9) 50%,
                    rgba(153, 27, 27, 0.85) 100%);
                border-radius: 20px;
                border: 4px solid #F87171;
                min-width: 400px;
                min-height: 200px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                text-align: center;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(243, 244, 246, 0.9));
                border: 3px solid #DC2626;
                border-radius: 12px;
                color: #991B1B;
                font-weight: bold;
                font-size: 16px;
                padding: 12px 24px;
                min-width: 120px;
                text-shadow: none;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 3px solid #F87171;
                transform: translateY(-2px);
            }
        """)

        # إضافة تأثير الظل للرسالة
        msg_shadow = QGraphicsDropShadowEffect()
        msg_shadow.setBlurRadius(30)
        msg_shadow.setColor(QColor(220, 38, 38, 150))
        msg_shadow.setOffset(0, 10)
        msg_box.setGraphicsEffect(msg_shadow)

        msg_box.exec_()

    def show_success_message(self, title, message):
        """عرض رسالة نجاح متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 15px;
                border: 2px solid #34D399;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #10B981;
                border-radius: 8px;
                color: #047857;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #34D399;
            }
        """)

        msg_box.exec_()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border-radius: 15px;
                border: 2px solid #F87171;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #DC2626;
                border-radius: 8px;
                color: #991B1B;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #F87171;
            }
        """)

        msg_box.exec_()

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح المحسنة"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if not self.is_loading:
                self.handle_enhanced_login()
        elif event.key() == Qt.Key_Escape:
            self.close_with_animation()
        elif event.key() == Qt.Key_F1:
            self.show_help_dialog()
        elif event.key() == Qt.Key_Tab:
            # التنقل بين الحقول
            if self.username_input.hasFocus():
                self.password_input.setFocus()
            else:
                self.username_input.setFocus()
        else:
            super().keyPressEvent(event)

    def mousePressEvent(self, event):
        """معالجة النقر بالماوس لسحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """معالجة حركة الماوس لسحب النافذة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def close_with_animation(self):
        """إغلاق النافذة مع رسوم متحركة"""
        # رسوم متحركة للإغلاق
        self.close_animation = QPropertyAnimation(self, b"windowOpacity")
        self.close_animation.setDuration(500)
        self.close_animation.setStartValue(1.0)
        self.close_animation.setEndValue(0.0)
        self.close_animation.setEasingCurve(QEasingCurve.InCubic)

        # إغلاق النافذة عند انتهاء الرسوم المتحركة
        self.close_animation.finished.connect(self.close)
        self.close_animation.start()

    def enterEvent(self, event):
        """عند دخول الماوس للنافذة"""
        # تأثير الإضاءة عند التمرير
        if hasattr(self, 'glow_effect'):
            self.glow_effect.setBlurRadius(60)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """عند خروج الماوس من النافذة"""
        # تقليل تأثير الإضاءة
        if hasattr(self, 'glow_effect'):
            self.glow_effect.setBlurRadius(50)
        super().leaveEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة المحسنة"""
        # إيقاف جميع المؤقتات
        if hasattr(self, 'background_wave_timer'):
            self.background_wave_timer.stop()
        if hasattr(self, 'border_flash_timer'):
            self.border_flash_timer.stop()

        # إغلاق جلسة قاعدة البيانات
        if self.session:
            self.session.close()

        # إيقاف جميع الرسوم المتحركة
        if hasattr(self, 'entrance_group'):
            self.entrance_group.stop()
        if hasattr(self, 'glow_animation'):
            self.glow_animation.stop()
        if hasattr(self, 'logo_pulse'):
            self.logo_pulse.stop()

        event.accept()

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        # تأكد من أن النافذة في المقدمة
        self.raise_()
        self.activateWindow()

        # تركيز على حقل اسم المستخدم
        if hasattr(self, 'username_input'):
            self.username_input.setFocus()

    def resizeEvent(self, event):
        """عند تغيير حجم النافذة"""
        super().resizeEvent(event)

        # تحديث حجم نظام الجسيمات والخلفية المتحركة
        if hasattr(self, 'particle_system'):
            self.particle_system.setGeometry(0, 0, self.width(), self.height())
        if hasattr(self, 'animated_bg'):
            self.animated_bg.setGeometry(0, 0, self.width(), self.height())


# اختبار شاشة الدخول
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد قاعدة البيانات للاختبار
    from database import init_db
    init_db()

    login_screen = LoginScreen()
    login_screen.show()

    sys.exit(app.exec_())
